package metrics

import "time"

// CollectorService ...
type CollectorService interface {
	CreateHistogramProcessor(name string, labels []string, buckets []float64) HistogramProcessor
	CreateCounterProcessor(name string, labels []string) CounterProcessor
}

// CounterProcessor ...
type CounterProcessor interface {
	Record(labels map[string]string)
}

// HistogramProcessor ...
type HistogramProcessor interface {
	RecordMillis(labels map[string]string, latency time.Duration)
	RecordSeconds(labels map[string]string, latency time.Duration)
}
