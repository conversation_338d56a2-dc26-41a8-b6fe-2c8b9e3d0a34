package metrics

import (
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"todo-app-api/internal/config"
)

var (
	collectorInstance *PrometheusCollector
	once              sync.Once
)

// NewPrometheusCollectorService returns a singleton instance of PrometheusCollector
func NewPrometheusCollectorService() *PrometheusCollector {
	once.Do(func() {
		collectorInstance = &PrometheusCollector{
			registry: prometheus.DefaultRegisterer.(*prometheus.Registry),
		}
	})

	return collectorInstance
}

type PrometheusCollector struct {
	registry  *prometheus.Registry
	histogram *prometheus.HistogramVec
	counter   *prometheus.CounterVec
}

// CreateHistogramProcessor creates a Prometheus histogram.
func (p *PrometheusCollector) CreateHistogramProcessor(name string, labels []string, buckets []float64) HistogramProcessor {
	if p.histogram == nil {
		p.histogram = prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    name,
			Buckets: buckets,
		}, labels)

		p.registry.MustRegister(p.histogram)
	}

	return &prometheusHistogramProcessor{histogram: p.histogram}
}

// CreateCounterProcessor creates a Prometheus counter.
func (p *PrometheusCollector) CreateCounterProcessor(name string, labels []string) CounterProcessor {
	if p.counter == nil {
		p.counter = prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: name,
		}, labels)

		p.registry.MustRegister(p.counter)
	}

	return &prometheusCounterProcessor{counter: p.counter}
}

func (p *PrometheusCollector) Serve() {
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", config.Config.Prometheus.Port), nil))
	}()
}

// PrometheusHistogramProcessor implements the HistogramProcessor interface.
type prometheusHistogramProcessor struct {
	histogram *prometheus.HistogramVec
}

func (p *prometheusHistogramProcessor) RecordMillis(labels map[string]string, latency time.Duration) {
	p.histogram.With(prometheus.Labels(labels)).Observe(float64(latency.Milliseconds()))
}

func (p *prometheusHistogramProcessor) RecordSeconds(labels map[string]string, latency time.Duration) {
	p.histogram.With(prometheus.Labels(labels)).Observe(latency.Seconds())
}

// PrometheusCounterProcessor implements the CounterProcessor interface.
type prometheusCounterProcessor struct {
	counter *prometheus.CounterVec
}

func (p *prometheusCounterProcessor) Record(labels map[string]string) {
	p.counter.With(prometheus.Labels(labels)).Inc()
}
