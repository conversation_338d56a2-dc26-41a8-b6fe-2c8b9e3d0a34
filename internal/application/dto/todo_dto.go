package dto

import (
	"time"

	"github.com/google/uuid"
)

// CreateTodoRequest represents the request to create a new todo
type CreateTodoRequest struct {
	Title       string     `json:"title" validate:"required,min=1,max=255"`
	Description string     `json:"description" validate:"max=1000"`
	Priority    int        `json:"priority" validate:"min=1,max=5"`
	DueDate     *time.Time `json:"due_date,omitempty"`
}

// UpdateTodoRequest represents the request to update an existing todo
type UpdateTodoRequest struct {
	Title       *string    `json:"title,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string    `json:"description,omitempty" validate:"omitempty,max=1000"`
	Completed   *bool      `json:"completed,omitempty"`
	Priority    *int       `json:"priority,omitempty" validate:"omitempty,min=1,max=5"`
	DueDate     *time.Time `json:"due_date,omitempty"`
}

// TodoResponse represents the response structure for a todo
type TodoResponse struct {
	ID          uuid.UUID  `json:"id"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Completed   bool       `json:"completed"`
	Priority    int        `json:"priority"`
	DueDate     *time.Time `json:"due_date,omitempty"`
	IsOverdue   bool       `json:"is_overdue"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// TodoListResponse represents the response structure for a list of todos
type TodoListResponse struct {
	Todos []TodoResponse `json:"todos"`
	Total int            `json:"total"`
}

// GetTodosQuery represents query parameters for getting todos
type GetTodosQuery struct {
	Completed *bool  `query:"completed"`
	Priority  *int   `query:"priority" validate:"omitempty,min=1,max=5"`
	Limit     *int   `query:"limit" validate:"omitempty,min=1,max=100"`
	Offset    *int   `query:"offset" validate:"omitempty,min=0"`
	SortBy    string `query:"sort_by" validate:"omitempty,oneof=created_at updated_at priority due_date title"`
	SortOrder string `query:"sort_order" validate:"omitempty,oneof=asc desc"`
}
