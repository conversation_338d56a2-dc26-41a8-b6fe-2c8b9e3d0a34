package services

import (
	"context"
	"fmt"

	"todo-app-api/internal/application/dto"
	"todo-app-api/internal/domain/entities"
	"todo-app-api/internal/domain/repositories"

	"github.com/google/uuid"
)

// TodoService handles business logic for todo operations
type TodoService struct {
	todoRepo repositories.TodoRepository
}

// NewTodoService creates a new todo service
func NewTodoService(todoRepo repositories.TodoRepository) *TodoService {
	return &TodoService{
		todoRepo: todoRepo,
	}
}

// CreateTodo creates a new todo
func (s *TodoService) CreateTodo(ctx context.Context, req dto.CreateTodoRequest) (*dto.TodoResponse, error) {
	todo, err := entities.NewTodo(req.Title, req.Description, req.Priority, req.DueDate)
	if err != nil {
		return nil, fmt.Errorf("failed to create todo entity: %w", err)
	}

	if err := s.todoRepo.Create(ctx, todo); err != nil {
		return nil, fmt.Errorf("failed to save todo: %w", err)
	}

	return s.todoToResponse(todo), nil
}

// GetTodoByID retrieves a todo by its ID
func (s *TodoService) GetTodoByID(ctx context.Context, id uuid.UUID) (*dto.TodoResponse, error) {
	todo, err := s.todoRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get todo: %w", err)
	}

	return s.todoToResponse(todo), nil
}

// GetAllTodos retrieves all todos with optional filtering
func (s *TodoService) GetAllTodos(ctx context.Context, query dto.GetTodosQuery) (*dto.TodoListResponse, error) {
	filters := repositories.TodoFilters{
		Completed: query.Completed,
		Priority:  query.Priority,
		Limit:     query.Limit,
		Offset:    query.Offset,
		SortBy:    query.SortBy,
		SortOrder: query.SortOrder,
	}

	todos, err := s.todoRepo.GetAll(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get todos: %w", err)
	}

	responses := make([]dto.TodoResponse, len(todos))
	for i, todo := range todos {
		responses[i] = *s.todoToResponse(todo)
	}

	return &dto.TodoListResponse{
		Todos: responses,
		Total: len(responses),
	}, nil
}

// UpdateTodo updates an existing todo
func (s *TodoService) UpdateTodo(ctx context.Context, id uuid.UUID, req dto.UpdateTodoRequest) (*dto.TodoResponse, error) {
	todo, err := s.todoRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get todo: %w", err)
	}

	// Apply updates
	if req.Title != nil {
		if err := todo.UpdateTitle(*req.Title); err != nil {
			return nil, fmt.Errorf("failed to update title: %w", err)
		}
	}

	if req.Description != nil {
		todo.UpdateDescription(*req.Description)
	}

	if req.Priority != nil {
		if err := todo.UpdatePriority(*req.Priority); err != nil {
			return nil, fmt.Errorf("failed to update priority: %w", err)
		}
	}

	if req.DueDate != nil {
		todo.UpdateDueDate(req.DueDate)
	}

	if req.Completed != nil {
		if *req.Completed {
			todo.MarkAsCompleted()
		} else {
			todo.MarkAsIncomplete()
		}
	}

	if err := s.todoRepo.Update(ctx, todo); err != nil {
		return nil, fmt.Errorf("failed to update todo: %w", err)
	}

	return s.todoToResponse(todo), nil
}

// DeleteTodo deletes a todo by its ID
func (s *TodoService) DeleteTodo(ctx context.Context, id uuid.UUID) error {
	if err := s.todoRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete todo: %w", err)
	}

	return nil
}

// todoToResponse converts a todo entity to a response DTO
func (s *TodoService) todoToResponse(todo *entities.Todo) *dto.TodoResponse {
	return &dto.TodoResponse{
		ID:          todo.ID(),
		Title:       todo.Title(),
		Description: todo.Description(),
		Completed:   todo.IsCompleted(),
		Priority:    todo.Priority(),
		DueDate:     todo.DueDate(),
		IsOverdue:   todo.IsOverdue(),
		CreatedAt:   todo.CreatedAt(),
		UpdatedAt:   todo.UpdatedAt(),
	}
}
