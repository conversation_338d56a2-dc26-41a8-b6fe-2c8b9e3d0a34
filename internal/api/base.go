package api

import (
	"context"
	"errors"

	"github.com/gofiber/fiber/v2"
)

type Response struct {
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Data      any    `json:"data"`
	Success   bool   `json:"success"`
}

func SuccessResponse(c *fiber.Ctx, ctx context.Context, data any) error {
	return c.Status(fiber.StatusOK).JSON(Response{
		Data:    data,
		Message: "success",
		// RequestID: requestID,
		Success: true,
	})
}

func ErrorResponse(c *fiber.Ctx, ctx context.Context, message string) error {
	return c.Status(fiber.StatusBadRequest).JSON(Response{
		Message: message,
		// RequestID: requestID,
		Success: false,
	})
}

// ErrorHandler handles default error on gofiber
func ErrorHandler(c *fiber.Ctx, err error) error {
	// Status code defaults to 500
	code := fiber.StatusInternalServerError

	// Retrieve the custom status code if it's a *fiber.Error
	var e *fiber.Error
	if errors.As(err, &e) {
		code = e.Code
	}

	// Send custom error
	return c.Status(code).JSON(fiber.Map{
		"success": false,
		"message": fiber.NewError(code).Error(),
		// "request_id": c.Locals(activity.FieldRequestID),
	})
}
