package routes

import (
	"todo-app-api/internal/api/handlers"

	"github.com/gofiber/fiber/v2"
)

func RegisterRoutes(app *fiber.App, handler handlers.TodoHandler) {
	// Version 1
	api := app.Group("/v1")

	// Todos ...
	api.Get("/todos", handler.GetAllTodos)
	api.Get("/todos/:id", handler.GetTodoByID)
	api.Post("/todos", handler.CreateTodo)
	api.Put("/todos/:id", handler.UpdateTodoByID)
	api.Delete("/todos/:id", handler.DeleteTodo)
}
