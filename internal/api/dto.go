package api

type CreateTodoV1Request struct {
	Title       string `json:"title"`
	Description string `json:"description"`
}

type UpdateTodoByIDV1Request struct {
	Title       *string `json:"title,omitempty"`
	Description *string `json:"description,omitempty"`
	IsComplete  *bool   `json:"is_complete,omitempty"`
}

type GetTodoByIDV1Response struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	IsComplete  bool   `json:"is_complete"`
}

type GetAllTodosV1Response struct {
	Todos []GetTodoByIDV1Response `json:"todos"`
	Total int                     `json:"total"`
}
