package handlers

import (
	"context"

	"github.com/gofiber/fiber/v2"
)

type TodoHandler interface {
	GetTodoByID(c *fiber.Ctx) error
	GetAllTodos(c *fiber.Ctx) error
	CreateTodo(c *fiber.Ctx) error
	UpdateTodoByID(c *fiber.Ctx) error
	DeleteTodo(c *fiber.Ctx) error
}

type todoHandler struct {
	todoSvc todo_svc.TodoService
}

func NewTodoHandler() TodoHandler {
	return &todoHandler{}
}

func (h *todoHandler) GetTodoByID(c *fiber.Ctx) error {
	ctx := context.TODO()
	todoID := c.Params("id")

	todo, err := h.todoSvc.GetTodoByID(ctx, todoID)
	if err != nil {
		return c.JSON(api.ErrorResponse{})
	}

	return c.JSON(api.GetTodoByIDV1Response{})
}
