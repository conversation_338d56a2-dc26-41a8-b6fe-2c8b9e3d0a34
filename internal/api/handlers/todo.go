package handlers

import (
	"context"
	"fmt"

	"github.com/gofiber/fiber/v2"

	"todo-app-api/internal/api"
	todoV1 "todo-app-api/internal/modules/todo/v1"
)

type TodoHandler interface {
	GetTodoByID(c *fiber.Ctx) error
	GetAllTodos(c *fiber.Ctx) error
	CreateTodo(c *fiber.Ctx) error
	UpdateTodoByID(c *fiber.Ctx) error
	DeleteTodo(c *fiber.Ctx) error
}

type todoHandler struct {
	todoSvc todoV1.Service
}

func NewTodoHandler(todoSvc todoV1.Service) TodoHandler {
	return &todoHandler{
		todoSvc: todoSvc,
	}
}

func (h *todoHandler) GetTodoByID(c *fiber.Ctx) error {
	ctx := context.TODO()
	todoID := c.Params("id")

	todo, err := h.todoSvc.GetTodoByID(ctx, todoID)
	if err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	return api.SuccessResponse(c, ctx, api.GetTodoByIDV1Response{
		ID:          todo.ID,
		Title:       todo.Title,
		Description: todo.Description,
		IsComplete:  todo.IsComplete,
	})
}

func (h *todoHandler) GetAllTodos(c *fiber.Ctx) error {
	ctx := context.TODO()

	todos, err := h.todoSvc.GetAllTodos(ctx)
	if err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	allTodos := []api.GetTodoByIDV1Response{}
	for _, todo := range todos {
		allTodos = append(allTodos, api.GetTodoByIDV1Response{
			ID:          todo.ID,
			Title:       todo.Title,
			Description: todo.Description,
			IsComplete:  todo.IsComplete,
		})
	}

	response := api.GetAllTodosV1Response{
		Todos: allTodos,
		Total: len(todos),
	}

	// Debug log
	fmt.Printf("Response data: %+v\n", response)

	return api.SuccessResponse(c, ctx, response)
}

func (h *todoHandler) CreateTodo(c *fiber.Ctx) error {
	ctx := context.TODO()

	var req api.CreateTodoV1Request
	if err := c.BodyParser(&req); err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	todo, err := h.todoSvc.CreateTodo(ctx, todoV1.Todo{
		Title:       req.Title,
		Description: req.Description,
	})
	if err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	return api.SuccessResponse(c, ctx, api.GetTodoByIDV1Response{
		ID:          todo.ID,
		Title:       todo.Title,
		Description: todo.Description,
		IsComplete:  todo.IsComplete,
	})
}

func (h *todoHandler) UpdateTodoByID(c *fiber.Ctx) error {
	ctx := context.TODO()
	todoID := c.Params("id")

	var req api.UpdateTodoByIDV1Request
	if err := c.BodyParser(&req); err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	err := h.todoSvc.UpdateTodoByID(ctx, todoID, todoV1.Todo{
		Title:       *req.Title,
		Description: *req.Description,
		IsComplete:  *req.IsComplete,
	})
	if err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	return api.SuccessResponse(c, ctx, nil)
}

func (h *todoHandler) DeleteTodo(c *fiber.Ctx) error {
	ctx := context.TODO()
	todoID := c.Params("id")

	err := h.todoSvc.DeleteTodo(ctx, todoID)
	if err != nil {
		return api.ErrorResponse(c, ctx, err.Error())
	}

	return api.SuccessResponse(c, ctx, nil)
}
