package middleware

import (
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"

	"todo-app-api/internal/monitoring/metrics"
)

func Metrics(metrics *metrics.PrometheusCollector) fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()

		counter := metrics.CreateCounterProcessor("todo_app_api_v2_http_requests_total", []string{"method", "path", "status_code"})
		histogram := metrics.CreateHistogramProcessor("todo_app_api_v2_http_request_duration_millis", []string{"method", "path"}, []float64{10, 20, 50, 100, 200, 500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 6000, 7000, 8000, 9000, 10000})

		err := c.Next()

		counter.Record(map[string]string{
			"method":      c.Method(),
			"path":        c.Path(),
			"status_code": strconv.Itoa(c.Response().StatusCode()),
		})

		histogram.RecordMillis(map[string]string{
			"method": c.Method(),
			"path":   c.Path(),
		}, time.Since(start))

		return err
	}
}
