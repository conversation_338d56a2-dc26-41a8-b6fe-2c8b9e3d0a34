package config

import (
	"path/filepath"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Environment is the running application mode
var Environment string = "dev"
var Config config

type DatabaseCfg struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"db_name"`
	SSLMode  string `mapstructure:"ssl_mode"`
}

type PrometheusCfg struct {
	Port int `mapstructure:"port"`
}

type ServerCfg struct {
	IdleTimeout int `mapstructure:"idle_timeout"`
	Port        int `mapstructure:"port"`
}

type config struct {
	Database   DatabaseCfg   `mapstructure:"database"`
	Prometheus PrometheusCfg `mapstructure:"prometheus"`
	Server     ServerCfg     `mapstructure:"server"`
}

func init() {
	var err error

	viper.SetEnvPrefix("CNX")
	viper.AutomaticEnv()

	// Set default values
	setDefaults()

	configName := "application"

	if viper.IsSet("ENV") {
		Environment = viper.GetString("ENV")
	}

	if Environment != "production" {
		configName = configName + "." + Environment
	}

	viper.SetConfigName(configName) // name of config file (without extension)
	viper.AddConfigPath(filepath.Join(GetAppBasePath(), "conf"))
	viper.AddConfigPath(".") // optionally look for config in the working directory

	// Try to read config file, but don't panic if it doesn't exist
	err = viper.ReadInConfig()
	if err != nil {
		logrus.Warnf("Config file not found, using defaults and environment variables: %v", err)
	} else {
		logrus.Infof("Using config file: %s", viper.ConfigFileUsed())
	}

	//Unmarshal application yml to config
	err = viper.Unmarshal(&Config)
	if err != nil {
		logrus.Errorf("unable to decode into struct, %v", err)
	}
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.idle_timeout", 60)

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", "5432")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "password")
	viper.SetDefault("database.db_name", "todoapp")
	viper.SetDefault("database.ssl_mode", "disable")

	// Prometheus defaults
	viper.SetDefault("prometheus.port", 4000)
}

func GetAppBasePath() string {
	basePath, _ := filepath.Abs(".")
	maxDepth := 10 // Prevent infinite loop
	depth := 0

	for filepath.Base(basePath) != "todo-app-api" && depth < maxDepth {
		parent := filepath.Dir(basePath)
		if parent == basePath {
			// Reached root directory, break to prevent infinite loop
			break
		}
		basePath = parent
		depth++
	}
	return basePath
}
