package config

import (
	"fmt"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Environment is the running application mode
var Environment string = "dev"
var Config config

type ServerCfg struct {
	IdleTimeout int `mapstructure:"idle_timeout"`
	Port        int `mapstructure:"port"`
}

type PrometheusCfg struct {
	Port int `mapstructure:"port"`
}

type config struct {
	Prometheus PrometheusCfg `mapstructure:"prometheus"`
	Server     ServerCfg     `mapstructure:"server"`
}

func init() {
	var err error

	viper.SetEnvPrefix("CNX")
	viper.AutomaticEnv()

	configName := "application"

	if viper.IsSet("ENV") {
		Environment = viper.GetString("ENV")
	}

	if Environment != "production" {
		configName = configName + "." + Environment
	}

	viper.SetConfigName(configName) // name of config file (without extension)
	viper.AddConfigPath(filepath.Join(GetAppBasePath(), "conf"))
	viper.AddConfigPath(".")   // optionally look for config in the working directory
	err = viper.ReadInConfig() // Find and read the config file
	if err != nil {            // Handle errors reading the config file
		panic(fmt.Errorf("fatal error config file: %s", err))
	}

	//Unmarshal application yml to config
	err = viper.Unmarshal(&Config)
	if err != nil {
		logrus.Errorf("unable to decode into struct, %v", err)
	}
}

func GetAppBasePath() string {
	basePath, _ := filepath.Abs(".")
	for filepath.Base(basePath) != "todo-app-api" {
		basePath = filepath.Dir(basePath)
	}
	return basePath
}
