package config

import (
	"path/filepath"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Environment is the running application mode
var Environment string = "dev"
var Config config

type DatabaseCfg struct {
	Host     string `mapstructure:"db_host"`
	Port     string `mapstructure:"db_port"`
	User     string `mapstructure:"db_user"`
	Password string `mapstructure:"db_password"`
	DBName   string `mapstructure:"db_name"`
	SSLMode  string `mapstructure:"db_sslmode"`
}

type PrometheusCfg struct {
	Port int `mapstructure:"port"`
}

type ServerCfg struct {
	IdleTimeout int `mapstructure:"idle_timeout"`
	Port        int `mapstructure:"port"`
}

type config struct {
	Database   DatabaseCfg   `mapstructure:"database"`
	Prometheus PrometheusCfg `mapstructure:"prometheus"`
	Server     ServerCfg     `mapstructure:"server"`
}

func init() {
	var err error

	viper.SetEnvPrefix("TODO_APP")
	viper.AutomaticEnv()

	// Set default values
	setDefaults()

	// Determine config file name based on environment
	configName := "dev" // default to dev

	if viper.IsSet("ENV") {
		Environment = viper.GetString("ENV")
	}

	// Use prod.yml for production, dev.yml for development
	if Environment == "production" {
		configName = "prod"
	}

	viper.SetConfigName(configName) // name of config file (without extension)
	viper.AddConfigPath(filepath.Join(GetAppBasePath(), "configs"))
	viper.AddConfigPath("./configs") // optionally look for config in the working directory
	viper.AddConfigPath(".")         // fallback to current directory

	// Try to read config file, but don't panic if it doesn't exist
	err = viper.ReadInConfig()
	if err != nil {
		logrus.Warnf("Config file not found (%s.yml), using defaults and environment variables: %v", configName, err)
	} else {
		logrus.Infof("Using config file: %s", viper.ConfigFileUsed())
	}

	//Unmarshal application yml to config
	err = viper.Unmarshal(&Config)
	if err != nil {
		logrus.Errorf("unable to decode into struct, %v", err)
	}
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.idle_timeout", 60)

	// Database defaults
	viper.SetDefault("database.db_host", "localhost")
	viper.SetDefault("database.db_port", "5432")
	viper.SetDefault("database.db_user", "postgres")
	viper.SetDefault("database.db_password", "password")
	viper.SetDefault("database.db_name", "todoapp")
	viper.SetDefault("database.db_sslmode", "disable")

	// Prometheus defaults
	viper.SetDefault("prometheus.port", 4000)
}

func GetAppBasePath() string {
	basePath, _ := filepath.Abs(".")
	maxDepth := 10 // Prevent infinite loop
	depth := 0

	for filepath.Base(basePath) != "todo-app-api" && depth < maxDepth {
		parent := filepath.Dir(basePath)
		if parent == basePath {
			// Reached root directory, break to prevent infinite loop
			break
		}
		basePath = parent
		depth++
	}
	return basePath
}
