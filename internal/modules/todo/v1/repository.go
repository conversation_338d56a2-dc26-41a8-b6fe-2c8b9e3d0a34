package todoV1

import (
	"context"
	"time"

	"github.com/go-pg/pg/v10"
)

// TodoDB represents database structure for table todos
type TodoDB struct {
	ID          string    `pg:"id,pk" json:"id"`
	Title       string    `pg:"title" json:"title"`
	Description string    `pg:"description" json:"description"`
	IsComplete  bool      `pg:"is_completed" json:"is_complete"`
	CreatedAt   time.Time `pg:"created_at" json:"created_at"`
	UpdatedAt   time.Time `pg:"updated_at" json:"updated_at"`
}

// TodoRepository interface for todo operations
type TodoRepository interface {
	Save(ctx context.Context, todo TodoDB) (TodoDB, error)
	FindByID(ctx context.Context, id string) (TodoDB, error)
	FindAll(ctx context.Context) ([]TodoDB, error)
	UpdateByID(ctx context.Context, todo TodoDB) error
	DeleteByID(ctx context.Context, id string) error
}

type todoRepository struct {
	db *pg.DB
}

func NewTodoRepository(db *pg.DB) TodoRepository {
	return &todoRepository{db: db}
}

func (r *todoRepository) Save(ctx context.Context, todo TodoDB) (TodoDB, error) {
	_, err := r.db.ModelContext(ctx, &todo).Insert()
	return todo, err
}

func (r *todoRepository) FindByID(ctx context.Context, id string) (TodoDB, error) {
	todo := TodoDB{ID: id}
	err := r.db.ModelContext(ctx, &todo).WherePK().Select()
	return todo, err
}

func (r *todoRepository) FindAll(ctx context.Context) ([]TodoDB, error) {
	var todos []TodoDB
	err := r.db.ModelContext(ctx, &todos).Select()
	return todos, err
}

func (r *todoRepository) UpdateByID(ctx context.Context, todo TodoDB) error {
	_, err := r.db.ModelContext(ctx, &todo).WherePK().Update()
	return err
}

func (r *todoRepository) DeleteByID(ctx context.Context, id string) error {
	todo := &TodoDB{ID: id}
	_, err := r.db.ModelContext(ctx, todo).WherePK().Delete()
	return err
}
