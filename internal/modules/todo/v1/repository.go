package todoV1

import (
	"context"
	"time"

	"github.com/go-pg/pg/v10"
	"github.com/google/uuid"
)

// TodoDB represents database structure for table todos
type TodoDB struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	IsComplete  bool      `json:"is_complete"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TodoRepository interface for todo operations
type TodoRepository interface {
	Save(ctx context.Context, todo TodoDB) (TodoDB, error)
	FindByID(ctx context.Context, id string) (TodoDB, error)
	FindAll(ctx context.Context) ([]TodoDB, error)
	UpdateByID(ctx context.Context, todo TodoDB) error
	DeleteByID(ctx context.Context, id string) error
}

type todoRepository struct {
	db *pg.DB
}

func NewTodoRepository(db *pg.DB) TodoRepository {
	return &todoRepository{db: db}
}

func (r *todoRepository) Save(ctx context.Context, todo TodoDB) (TodoDB, error) {
	// Generate UUID if not provided
	if todo.ID == "" {
		todo.ID = uuid.New().String()
	}

	// Set timestamps
	now := time.Now()
	todo.CreatedAt = now
	todo.UpdatedAt = now

	// Use raw SQL query
	query := `
		INSERT INTO todos (id, title, description, is_complete, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id, title, description, is_complete, created_at, updated_at
	`

	var result TodoDB
	_, err := r.db.QueryOneContext(ctx, &result, query,
		todo.ID, todo.Title, todo.Description, todo.IsComplete, todo.CreatedAt, todo.UpdatedAt)

	if err != nil {
		return TodoDB{}, err
	}

	return result, nil
}

func (r *todoRepository) FindByID(ctx context.Context, id string) (TodoDB, error) {
	query := `
		SELECT id, title, description, is_complete, created_at, updated_at
		FROM todos
		WHERE id = $1
	`

	var todo TodoDB
	_, err := r.db.QueryOneContext(ctx, &todo, query, id)

	if err != nil {
		return TodoDB{}, err
	}

	return todo, nil
}

func (r *todoRepository) FindAll(ctx context.Context) ([]TodoDB, error) {
	query := `
		SELECT id, title, description, is_complete, created_at, updated_at
		FROM todos
		ORDER BY created_at DESC
	`

	var todos []TodoDB
	_, err := r.db.QueryContext(ctx, &todos, query)

	if err != nil {
		return nil, err
	}

	return todos, nil
}

func (r *todoRepository) UpdateByID(ctx context.Context, todo TodoDB) error {
	// Set updated timestamp
	todo.UpdatedAt = time.Now()

	query := `
		UPDATE todos
		SET title = $2, description = $3, is_complete = $4, updated_at = $5
		WHERE id = $1
	`

	_, err := r.db.ExecContext(ctx, query,
		todo.ID, todo.Title, todo.Description, todo.IsComplete, todo.UpdatedAt)

	return err
}

func (r *todoRepository) DeleteByID(ctx context.Context, id string) error {
	query := `DELETE FROM todos WHERE id = $1`

	_, err := r.db.ExecContext(ctx, query, id)

	return err
}
