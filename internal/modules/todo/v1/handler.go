package todoV1

import (
	"context"
	"fmt"
)

func NewService(repo TodoRepository) Service {
	return &service{repo: repo}
}

type service struct {
	repo TodoRepository
}

func (s *service) CreateTodo(ctx context.Context, req Todo) (*Todo, error) {
	// Repository will handle UUID generation and timestamps
	todo, err := s.repo.Save(ctx, TodoDB{
		Title:       req.Title,
		Description: req.Description,
		IsComplete:  false,
	})
	if err != nil {
		return nil, err
	}

	return &Todo{
		ID:          todo.ID,
		Title:       todo.Title,
		Description: todo.Description,
		IsComplete:  todo.IsComplete,
	}, nil
}

func (s *service) GetTodoByID(ctx context.Context, id string) (*Todo, error) {
	todo, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return &Todo{
		ID:          todo.ID,
		Title:       todo.Title,
		Description: todo.Description,
		IsComplete:  todo.IsComplete,
	}, nil
}

func (s *service) GetAllTodos(ctx context.Context) ([]Todo, error) {
	todos, err := s.repo.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	fmt.Println("Nih: ", todos)

	allTodos := []Todo{}
	for _, todo := range todos {
		allTodos = append(allTodos, Todo{
			ID:          todo.ID,
			Title:       todo.Title,
			Description: todo.Description,
			IsComplete:  todo.IsComplete,
		})
	}

	return allTodos, nil
}

func (s *service) UpdateTodoByID(ctx context.Context, id string, req Todo) error {
	todo, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	// Update fields - repository will handle timestamp
	todo.Title = req.Title
	todo.Description = req.Description
	todo.IsComplete = req.IsComplete

	err = s.repo.UpdateByID(ctx, todo)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) DeleteTodo(ctx context.Context, id string) error {
	err := s.repo.DeleteByID(ctx, id)
	if err != nil {
		return err
	}

	return nil
}
