package database

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
	"todo-app-api/internal/config"

	"github.com/go-pg/pg/v10"
)

// Config holds database configuration
type Config struct {
	Host            string
	Port            string
	User            string
	Password        string
	DBName          string
	SSLMode         string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
}

// NewConfig creates a new database configuration from the main config
func NewConfig() *Config {
	return &Config{
		Host:            config.Config.Database.Host,
		Port:            config.Config.Database.Port,
		User:            config.Config.Database.User,
		Password:        config.Config.Database.Password,
		DBName:          config.Config.Database.DBName,
		SSLMode:         config.Config.Database.SSLMode,
		MaxOpenConns:    getEnvInt("TODO_APP_DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:    getEnvInt("TODO_APP_DB_MAX_IDLE_CONNS", 5),
		ConnMaxLifetime: getEnvDuration("TODO_APP_DB_CONN_MAX_LIFETIME", 5*time.Minute),
		ConnMaxIdleTime: getEnvDuration("TODO_APP_DB_CONN_MAX_IDLE_TIME", 5*time.Minute),
	}
}

// Connect establishes a go-pg connection to the PostgreSQL database
func Connect(config *Config) (*pg.DB, error) {
	opts := &pg.Options{
		Addr:     fmt.Sprintf("%s:%s", config.Host, config.Port),
		User:     config.User,
		Password: config.Password,
		Database: config.DBName,
		PoolSize: config.MaxOpenConns,
	}

	// Handle SSL mode
	if config.SSLMode == "disable" {
		opts.TLSConfig = nil
	}

	db := pg.Connect(opts)

	// Test the connection
	var n int
	_, err := db.QueryOne(pg.Scan(&n), "SELECT 1")
	if err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Printf("Successfully connected to database: %s:%s/%s", config.Host, config.Port, config.DBName)
	return db, nil
}

// Helper functions for environment variable handling

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvRequired gets a required environment variable and panics if not set
func getEnvRequired(key string) string {
	value := os.Getenv(key)
	if value == "" {
		log.Fatalf("Required environment variable %s is not set", key)
	}
	return value
}

// getEnvInt gets an environment variable as integer with fallback
func getEnvInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Warning: Invalid integer value for %s, using fallback %d", key, fallback)
	}
	return fallback
}

// getEnvDuration gets an environment variable as duration with fallback
func getEnvDuration(key string, fallback time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
		log.Printf("Warning: Invalid duration value for %s, using fallback %v", key, fallback)
	}
	return fallback
}
