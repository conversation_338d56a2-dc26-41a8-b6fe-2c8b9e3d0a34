package database

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
	"todo-app-api/internal/config"

	"github.com/go-pg/pg/v10"
)

// Config holds database configuration
type Config struct {
	Host            string
	Port            string
	User            string
	Password        string
	DBName          string
	SSLMode         string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
}

// NewConfig creates a new database configuration from the main config with fallbacks
func NewConfig() *Config {
	// Use config values with environment variable fallbacks
	host := config.Config.Database.Host
	if host == "" {
		host = getEnv("TODO_APP_DB_HOST", "localhost")
	}

	port := config.Config.Database.Port
	if port == "" {
		port = getEnv("TODO_APP_DB_PORT", "5432")
	}

	user := config.Config.Database.User
	if user == "" {
		user = getEnv("TODO_APP_DB_USER", "postgres")
	}

	password := config.Config.Database.Password
	if password == "" {
		password = getEnv("TODO_APP_DB_PASSWORD", "password")
	}

	dbname := config.Config.Database.DBName
	if dbname == "" {
		dbname = getEnv("TODO_APP_DB_NAME", "todoapp")
	}

	sslmode := config.Config.Database.SSLMode
	if sslmode == "" {
		sslmode = getEnv("TODO_APP_DB_SSLMODE", "disable")
	}

	return &Config{
		Host:            host,
		Port:            port,
		User:            user,
		Password:        password,
		DBName:          dbname,
		SSLMode:         sslmode,
		MaxOpenConns:    getEnvInt("TODO_APP_DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:    getEnvInt("TODO_APP_DB_MAX_IDLE_CONNS", 5),
		ConnMaxLifetime: getEnvDuration("TODO_APP_DB_CONN_MAX_LIFETIME", 5*time.Minute),
		ConnMaxIdleTime: getEnvDuration("TODO_APP_DB_CONN_MAX_IDLE_TIME", 5*time.Minute),
	}
}

// Connect establishes a go-pg connection to the PostgreSQL database
func Connect(config *Config) (*pg.DB, error) {
	opts := &pg.Options{
		Addr:     fmt.Sprintf("%s:%s", config.Host, config.Port),
		User:     config.User,
		Password: config.Password,
		Database: config.DBName,
		PoolSize: config.MaxOpenConns,
	}

	// Handle SSL mode
	if config.SSLMode == "disable" {
		opts.TLSConfig = nil
	}

	db := pg.Connect(opts)

	// Test the connection
	var n int
	_, err := db.QueryOne(pg.Scan(&n), "SELECT 1")
	if err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Printf("Successfully connected to database: %s:%s/%s", config.Host, config.Port, config.DBName)
	return db, nil
}

// Helper functions for environment variable handling

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvRequired gets a required environment variable and panics if not set
func getEnvRequired(key string) string {
	value := os.Getenv(key)
	if value == "" {
		log.Fatalf("Required environment variable %s is not set", key)
	}
	return value
}

// getEnvInt gets an environment variable as integer with fallback
func getEnvInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Warning: Invalid integer value for %s, using fallback %d", key, fallback)
	}
	return fallback
}

// getEnvDuration gets an environment variable as duration with fallback
func getEnvDuration(key string, fallback time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
		log.Printf("Warning: Invalid duration value for %s, using fallback %v", key, fallback)
	}
	return fallback
}
