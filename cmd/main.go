package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
	"todo-app-api/internal/api"
	"todo-app-api/internal/api/handlers"
	"todo-app-api/internal/api/routes"
	"todo-app-api/internal/config"
	"todo-app-api/internal/database"
	"todo-app-api/internal/middleware"
	todoV1 "todo-app-api/internal/modules/todo/v1"
	"todo-app-api/internal/monitoring/metrics"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/healthcheck"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"
	"github.com/gofiber/fiber/v2/utils"
)

func main() {
	// Initiation engine
	collectorService := metrics.NewPrometheusCollectorService()

	// Database initialization
	dbConfig := database.NewConfig()
	db, err := database.Connect(dbConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Initiation service with database
	todoRepo := todoV1.NewTodoRepository(db)
	todoSvc := todoV1.NewService(todoRepo)

	// Fiber ...
	app := fiber.New(fiber.Config{
		Immutable:    true, // WARN: Don't delete config, this config use for immutable data context in fiber (see: https://docs.gofiber.io/#zero-allocation)
		IdleTimeout:  time.Duration(config.Config.Server.IdleTimeout) * time.Second,
		ErrorHandler: api.ErrorHandler,
	})

	app.Use(
		middleware.Metrics(collectorService),

		// Recover ...
		recover.New(recover.Config{
			EnableStackTrace: true,
		}),

		// Request ID Generator ...
		requestid.New(requestid.Config{
			Generator: utils.UUIDv4,
			Header:    "X-Request-ID",
		}),

		// Healthcheck ...
		healthcheck.New(healthcheck.Config{
			LivenessEndpoint: "/v1/ping",
		}),

		// Logger ...
		logger.New(logger.Config{
			Format: "ReqID: ${locals:request_id} | ${latency} | ${status} - ${method} ${path} | ${body}\n",
		}),
	)

	// Registering routes ...
	todoCtrl := handlers.NewTodoHandler(todoSvc)
	routes.RegisterRoutes(app, todoCtrl)

	// Start server
	go func() {
		if err := app.Listen(fmt.Sprintf(":%d", config.Config.Server.Port)); err != nil {
			log.Panic(err)
		}
	}()

	// Start serve metrics
	collectorService.Serve()

	c := make(chan os.Signal, 1)                    // Create channel to signify a signal being sent
	signal.Notify(c, os.Interrupt, syscall.SIGTERM) // When an interrupt or termination signal is sent, notify the channel

	_ = <-c // This blocks the main thread until an interrupt is received
	fmt.Println("Gracefully shutting down...")
	_ = app.Shutdown()

	fmt.Println("Todo App API was successful shutdown.")
}
