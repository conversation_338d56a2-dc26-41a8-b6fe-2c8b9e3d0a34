version: "3.8"
services:
  api:
    build:
      dockerfile: Dockerfile
      context: .
    volumes:
      - .:/go/src/todo-app-api
    ports:
      - 8080:8080 # API server
      - 4000:4000 # Metrics server
    networks:
      - todo-app-network
  
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - todo-app-network
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    networks:
      - todo-app-network

networks:
  todo-app-network: