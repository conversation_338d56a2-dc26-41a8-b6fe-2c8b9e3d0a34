version: "3.8"
services:
  api:
    build:
      dockerfile: deployments/Dockerfile
      context: ..
    volumes:
      - ..:/app
    ports:
      - 8080:8080 # API server
      - 4000:4000 # Metrics server
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=todouser
      - DB_PASSWORD=todopass
      - DB_NAME=todoapp
      - DB_SSLMODE=disable
    depends_on:
      - postgres
    networks:
      - todo-app-network

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: todoapp
      POSTGRES_USER: todouser
      POSTGRES_PASSWORD: todopass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - todo-app-network
  
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - todo-app-network
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    networks:
      - todo-app-network
  
  adminer:
    image: adminer
    container_name: adminer3
    restart: unless-stopped
    ports:
      - "8081:8080"
    networks:
      - todo-app-network

networks:
  todo-app-network:

volumes:
  postgres_data: